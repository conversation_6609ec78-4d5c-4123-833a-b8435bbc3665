I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The user wants to implement a basic MCP JSON-RPC 2.0 server with Express.js that can handle `tools/list` requests and return proper error responses. Based on the specifications in `PHASE_0_PREPARATION.md`, I need to create an Express application that integrates with the existing `MCPServer` class, implement proper JSON-RPC 2.0 protocol handling, ensure correct error codes are returned, and write comprehensive integration tests to validate MCP protocol compliance. The specifications provide exact implementations and test cases that need to be implemented.

### Approach

I'll implement a basic MCP JSON-RPC 2.0 server using Express.js that handles the `tools/list` method and proper error responses. The approach follows the exact specifications from `PHASE_0_PREPARATION.md`, creating an Express application that integrates with the existing `MCPServer` class, implements proper JSON-RPC 2.0 protocol handling with correct error codes (-32601, -32602, -32603), and includes comprehensive integration tests to validate MCP protocol compliance. The implementation will use the existing core interfaces and types, create mock implementations for testing, and ensure proper request/response formatting according to the JSON-RPC 2.0 specification.

### Reasoning

I analyzed the user's requirements and read the detailed specifications in `PHASE_0_PREPARATION.md` and `CONCEPTION.md`. The preparation document contains exact implementations for the `MCPServer` class with JSON-RPC 2.0 handling, integration test specifications, and the required Express.js setup. I also found the test structure showing how to create a test Express app that integrates with the MCPServer for protocol compliance validation.

## Mermaid Diagram

sequenceDiagram
    participant Client as MCP Client
    participant Express as Express Server
    participant MCPServer as MCPServer
    participant StateRepo as MockStateRepository
    participant Tool as ProjectTool

    Note over Client,Tool: MCP JSON-RPC 2.0 Implementation

    Client->>Express: POST /mcp (JSON-RPC request)
    Express->>Express: Validate JSON format
    Express->>Express: Check jsonrpc: "2.0"
    
    alt Valid JSON-RPC Request
        Express->>MCPServer: handleJSONRPC(request)
        
        alt tools/list method
            MCPServer->>MCPServer: handleToolsList()
            MCPServer-->>Express: JSON-RPC response (tools array)
        
        else tools/call method
            MCPServer->>Tool: execute(params, context)
            Tool->>StateRepo: createProject() / getProject()
            StateRepo-->>Tool: Project data
            Tool-->>MCPServer: MCPToolResult
            MCPServer-->>Express: JSON-RPC response (result)
        
        else invalid method
            MCPServer->>MCPServer: createErrorResponse(-32601)
            MCPServer-->>Express: JSON-RPC error response
        end
        
        Express-->>Client: HTTP 200 + JSON-RPC response
    
    else Invalid JSON-RPC Format
        Express->>Express: createErrorResponse(-32600)
        Express-->>Client: HTTP 400 + Error response
    
    else JSON Parse Error
        Express->>Express: createErrorResponse(-32700)
        Express-->>Client: HTTP 400 + Parse error
    end

    Note over Client,Tool: Error Code Compliance
    Note over Express: -32601: Method not found
    Note over Express: -32602: Invalid params
    Note over Express: -32603: Internal error

## Proposed File Changes

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\server.ts(NEW)

Create the main Express.js server application that integrates with the `MCPServer` class from `src/core/server/MCPServer.ts`. Set up Express with JSON middleware, create a POST route at `/mcp` that handles JSON-RPC 2.0 requests, implement proper request validation for JSON-RPC format, integrate with the MCPServer's `handleJSONRPC` method, and include comprehensive error handling for malformed requests, parsing errors, and server errors. The server should validate that incoming requests have the required `jsonrpc: '2.0'` field and return appropriate error responses for invalid requests. Include proper CORS configuration and request logging for development.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\state\MockStateRepository.ts(NEW)

Create a mock implementation of the `IStateRepository` interface from `src/core/interfaces/IStateRepository.ts` for testing and development purposes. Implement all required methods (createProject, getProject, updateProject, listProjects, deleteProject, saveSpecification, getSpecification, listSpecifications) with in-memory storage using Maps. Include proper error handling for not found cases, generate UUIDs for new projects, and maintain proper timestamps. This mock will be used by the Express server for demonstration and testing until the real persistence layer is implemented in later phases.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\workflow\MockWorkflowManager.ts(NEW)

Create a mock implementation of the `IWorkflowManager` interface from `src/core/interfaces/IWorkflowManager.ts` for testing and development purposes. Implement all required methods (validateTransition, executeTransition, getCurrentPhase, getAvailableTransitions, getPhaseRequirements) with basic workflow logic that follows the SDD phase progression (NEW → REQUIREMENTS → DESIGN → TASKS → EXECUTION → COMPLETED). Include validation rules for phase transitions and return appropriate error messages for invalid transitions. This mock will be used by the Express server for demonstration until the real workflow engine is implemented.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\tools\project\ProjectInitTool.ts(NEW)

Create a basic implementation of the `sdd_project_init` tool that implements the `IMCPToolHandler` interface from `src/core/interfaces/IMCPToolHandler.ts`. Define the tool with name 'sdd_project_init', description for initializing SDD projects, and JSON schema for input validation (name, description, techStack). Implement the execute method to create a new project using the StateRepository, validate input parameters, and return appropriate MCPToolResult with success/error content. This tool will be registered with the MCPServer to demonstrate the tools/list and tools/call functionality.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\tools\project\ProjectStatusTool.ts(NEW)

Create a basic implementation of the `sdd_project_status` tool that implements the `IMCPToolHandler` interface from `src/core/interfaces/IMCPToolHandler.ts`. Define the tool with name 'sdd_project_status', description for retrieving project status, and JSON schema for input validation (projectId). Implement the execute method to retrieve project information using the StateRepository, format the response with project details and current phase, and return appropriate MCPToolResult. This tool will be registered with the MCPServer to provide multiple tools for the tools/list demonstration.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\tests\helpers\test-app.ts(NEW)

Create the test helper function `createTestApp` exactly as referenced in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Set up an Express application with the MCPServer integration, create mock implementations of IStateRepository and IWorkflowManager using the mock classes from `src/state/MockStateRepository.ts` and `src/workflow/MockWorkflowManager.ts`, register sample tools (ProjectInitTool and ProjectStatusTool), configure the POST `/mcp` route that handles JSON-RPC requests, implement proper JSON parsing and error handling, and return the configured Express app for testing. Include proper error responses for malformed JSON and invalid JSON-RPC requests.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\tests\integration\mcp-basic.test.ts(NEW)

Create the integration test exactly as specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Import supertest and the createTestApp helper from `../helpers/test-app.ts`. Implement comprehensive tests for MCP JSON-RPC 2.0 protocol compliance: test `tools/list` request returns proper JSON-RPC 2.0 response format with tools array, test invalid method returns -32601 error code with proper error structure, test malformed JSON-RPC requests (missing jsonrpc field) return 400 status, test tools/call with valid tool execution, test tools/call with invalid tool name returns -32602 error, and test internal server errors return -32603 error code. Verify all response formats match JSON-RPC 2.0 specification exactly.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\tests\integration\mcp-tools.test.ts(NEW)

Create additional integration tests specifically for MCP tools functionality. Test the complete tools workflow: verify tools/list returns the registered tools (sdd_project_init and sdd_project_status) with correct schemas, test tools/call with sdd_project_init tool creates a project successfully, test tools/call with sdd_project_status retrieves project information, test tools/call with invalid parameters returns proper validation errors, and test tools/call with non-existent tool returns -32602 error. Include tests for edge cases like empty parameters, malformed tool arguments, and concurrent tool executions. Verify that tool responses follow the MCPToolResult format specified in the core types.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\tests\unit\server\express-server.test.ts(NEW)

Create unit tests for the Express server implementation in `src/server.ts`. Test the Express middleware configuration, JSON parsing middleware, CORS setup, and route handling. Test the `/mcp` POST route with various request scenarios: valid JSON-RPC requests, invalid JSON payloads, missing Content-Type headers, and oversized requests. Mock the MCPServer's handleJSONRPC method to test the integration layer without testing the actual MCP logic. Verify that the Express server properly forwards requests to MCPServer and returns responses with correct HTTP status codes and headers.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\tests\unit\tools\project-tools.test.ts(NEW)

Create unit tests for the project tools (ProjectInitTool and ProjectStatusTool). Test the ProjectInitTool: verify tool metadata (name, description, inputSchema), test execute method with valid parameters creates project via StateRepository, test execute method with invalid parameters returns error MCPToolResult, and test execute method handles StateRepository errors gracefully. Test the ProjectStatusTool: verify tool metadata, test execute method with valid projectId retrieves project information, test execute method with non-existent projectId returns appropriate error, and test execute method handles StateRepository errors. Mock the StateRepository dependency to isolate tool logic testing.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\index.ts(NEW)

Update the main entry point to import and start the Express server from `./server.ts`. Remove the previous stub implementation and replace it with proper server initialization that creates the Express app, sets up the MCPServer with mock dependencies, registers the project tools, and starts the HTTP server on the configured port (default 3000). Include proper error handling for server startup, graceful shutdown handling, and environment-based configuration. Add logging to indicate when the server is ready and listening for MCP requests.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\package.json(NEW)

Update package.json to include the Express.js dependencies and development scripts needed for the MCP server. Add dependencies: express, cors, and their TypeScript types (@types/express, @types/cors). Add supertest as a devDependency for integration testing. Update the scripts section to include 'dev': 'ts-node src/index.ts' for development server, 'start': 'node dist/index.js' for production, and ensure the existing build, test, and lint scripts are properly configured. Verify that all dependencies specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md` are included.